const { repeat } = require("lodash");
const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const Visit = sequelize.define(
    "Visit",
    {
      visit_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      title: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      type: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      category: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      start_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
        comment: "Date when the visit starts",
      },
      start_time: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      duration: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      end_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: "Date when the visit starts",
      },

      repeat_visit: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },

      facility_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "facility",
          key: "facility_id",
        },
        onDelete: "CASCADE",
        comment: "Reference to facility where visit takes place",
      },
      access_level_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "access_level",
          key: "access_level_id",
        },
        onDelete: "CASCADE",
      },
      host_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
        comment: "Reference to identity who is hosting the visit",
      },
      check_in_instruction: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      escort_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "SET NULL",
        comment: "Reference to identity who is escorting the visit (optional)",
      },
      send_notification: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
        comment: "Reference to identity who is hosting the visit",
      },

      remind_me: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },

      visitor_message: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "visit",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          fields: ["facility_id"],
        },
        {
          fields: ["host_id"],
        },
        {
          fields: ["escort_id"],
        },
        {
          fields: ["start_date"],
        },
      ],
    }
  );

  Visit.associate = (models) => {
    // Belongs to Facility
    Visit.belongsTo(models.Facility, {
      foreignKey: "facility_id",
      as: "facility",
    });
    Visit.belongsTo(models.MasterData, {
      foreignKey: "type",
      targetKey: "key",
      as: "visit_type_name",
      constraints: false,
      scope: {
        group: "visit_type",
      },
    });

    Visit.belongsTo(models.MasterData, {
      foreignKey: "category",
      targetKey: "key",
      as: "visit_category_name",
      constraints: false,
      scope: {
        group: "visit_category",
      },
    });

    Visit.belongsTo(models.MasterData, {
      foreignKey: "repeat_visit",
      targetKey: "key",
      as: "repeat_visit_name",
      constraints: false,
      scope: {
        group: "repeat_visit",
      },
    });

    Visit.belongsTo(models.MasterData, {
      foreignKey: "check_in_instruction",
      targetKey: "key",
      as: "check_in_instruction_name",
      constraints: false,
      scope: {
        group: "check_in_instruction",
      },
    });

    Visit.belongsTo(models.MasterData, {
      foreignKey: "remind_me",
      targetKey: "key",
      as: "remind_me_name",
      constraints: false,
      scope: {
        group: "remind_me",
      },
    });
    Visit.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "visit_status_name",
      constraints: false,
      scope: {
        group: "visit_status",
      },
    });

    // Belongs to Identity (Host)
    Visit.belongsTo(models.Identity, {
      foreignKey: "host_id",
      as: "host",
    });

    // Belongs to Identity (Escort) - optional
    Visit.belongsTo(models.Identity, {
      foreignKey: "escort_id",
      as: "escort",
    });

    // Many-to-many relationship with Guest through GuestVisit
    Visit.belongsToMany(models.Guest, {
      through: models.GuestVisit,
      foreignKey: "visit_id",
      otherKey: "guest_id",
      as: "guests",
    });

    // Direct association with GuestVisit for easier querying
    Visit.hasMany(models.GuestVisit, {
      foreignKey: "visit_id",
      as: "guestVisits",
    });
  };

  // Apply plugins
  history(Visit, sequelize, DataTypes);

  return Visit;
};
